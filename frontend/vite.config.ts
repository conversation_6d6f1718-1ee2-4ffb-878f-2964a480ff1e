import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react(), tailwindcss(), tsconfigPaths(), reactRouter()],
  ssr: {
    noExternal: [
      /^@adobe\/.*/,
      /^@react-spectrum\/.*/,
      /^@react-aria\/.*/,
      /^@react-stately\/.*/,
      /^@react-types\/.*/,
      /^@spectrum-icons\/.*/,
      /^@internationalized\/.*/,
      "client-only",
    ],
  },
  build: {
    commonjsOptions: {
      include: [/node_modules/],
    },
  },
  optimizeDeps: {
    include: [
      "@adobe/react-spectrum",
      "@react-spectrum/provider",
      "@react-spectrum/theme-default",
    ],
  },
});
