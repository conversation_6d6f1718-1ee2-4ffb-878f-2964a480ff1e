{"name": "ryandone-chatterbox-frontend", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@adobe/react-spectrum": "^3.45.0", "@react-router/node": "^7.9.2", "@react-router/serve": "^7.9.2", "isbot": "^5.1.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^7.9.2"}, "devDependencies": {"@react-router/dev": "^7.9.2", "@tailwindcss/vite": "^4.1.13", "@types/node": "^22", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.1", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite": "^7.1.7", "vite-tsconfig-paths": "^5.1.4"}}